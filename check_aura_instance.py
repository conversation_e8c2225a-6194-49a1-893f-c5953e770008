#!/usr/bin/env python3
"""
Neo4j Aura Instance Information Checker

This script provides detailed information about your Neo4j Aura instance
and explains GDS availability options.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AuraInstanceChecker:
    """Check Aura instance details and GDS options"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Connecting to Neo4j at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def get_instance_details(self):
        """Get detailed information about the Aura instance"""
        logger.info("\n🔍 Analyzing Neo4j Aura Instance...")
        
        with self.driver.session() as session:
            try:
                # Get Neo4j version and edition
                result = session.run("CALL dbms.components()")
                components = list(result)
                
                neo4j_version = None
                edition = None
                
                for component in components:
                    if component['name'] == 'Neo4j Kernel':
                        neo4j_version = component['versions'][0]
                        edition = component['edition']
                        break
                
                logger.info(f"📊 Instance Details:")
                logger.info(f"   Neo4j Version: {neo4j_version}")
                logger.info(f"   Edition: {edition}")
                logger.info(f"   URI: {self.uri}")
                
                # Determine Aura tier based on URI pattern
                if "databases.neo4j.io" in self.uri:
                    if self.uri.startswith("neo4j+s://"):
                        logger.info(f"   Aura Tier: AuraDB (Standard)")
                        return "auradb"
                    else:
                        logger.info(f"   Aura Tier: Unknown")
                        return "unknown"
                else:
                    logger.info(f"   Aura Tier: Self-hosted or Enterprise")
                    return "enterprise"
                    
            except Exception as e:
                logger.error(f"❌ Error getting instance details: {e}")
                return "unknown"
    
    def check_available_procedures(self):
        """Check what procedures are available"""
        logger.info("\n🔧 Checking Available Procedures...")
        
        with self.driver.session() as session:
            try:
                # Try to get available procedures (may not work in all Aura tiers)
                try:
                    result = session.run("SHOW PROCEDURES")
                    procedures = list(result)
                    
                    gds_procedures = [p for p in procedures if 'gds' in str(p).lower()]
                    apoc_procedures = [p for p in procedures if 'apoc' in str(p).lower()]
                    
                    logger.info(f"📋 Total Procedures: {len(procedures)}")
                    logger.info(f"   GDS Procedures: {len(gds_procedures)}")
                    logger.info(f"   APOC Procedures: {len(apoc_procedures)}")
                    
                    if gds_procedures:
                        logger.info("✅ GDS procedures found!")
                        for proc in gds_procedures[:5]:  # Show first 5
                            logger.info(f"      - {proc}")
                    else:
                        logger.info("❌ No GDS procedures found")
                        
                except Exception as e:
                    logger.info(f"ℹ️  SHOW PROCEDURES not available: {e}")
                    
                    # Try alternative method
                    test_procedures = [
                        "apoc.version",
                        "gds.version", 
                        "db.info",
                        "dbms.info"
                    ]
                    
                    available = []
                    for proc in test_procedures:
                        try:
                            session.run(f"CALL {proc}()")
                            available.append(proc)
                        except:
                            pass
                    
                    logger.info(f"📋 Available test procedures: {available}")
                    
            except Exception as e:
                logger.error(f"❌ Error checking procedures: {e}")
    
    def explain_gds_options(self, aura_tier):
        """Explain GDS options based on Aura tier"""
        logger.info("\n💡 GDS Library Options:")
        
        if aura_tier == "auradb":
            logger.info("📋 You are using Neo4j AuraDB (Standard)")
            logger.info("❌ GDS Library is NOT available in AuraDB")
            logger.info("")
            logger.info("🔄 Options to get GDS access:")
            logger.info("   1. 🚀 Upgrade to Neo4j AuraDS (Data Science)")
            logger.info("      - Includes full GDS library")
            logger.info("      - Optimized for graph analytics")
            logger.info("      - Higher cost but includes advanced algorithms")
            logger.info("")
            logger.info("   2. 🏢 Use Neo4j Enterprise (self-hosted)")
            logger.info("      - Install GDS plugin separately")
            logger.info("      - Full control over configuration")
            logger.info("      - Requires infrastructure management")
            logger.info("")
            logger.info("   3. 🔧 Alternative: Use built-in Cypher algorithms")
            logger.info("      - Limited but some graph algorithms available")
            logger.info("      - No advanced community detection")
            logger.info("      - Can implement basic centrality measures")
            
        elif aura_tier == "enterprise":
            logger.info("📋 You appear to be using Enterprise/Self-hosted Neo4j")
            logger.info("💡 GDS should be available if installed")
            logger.info("🔧 Check if GDS plugin is installed and enabled")
            
        else:
            logger.info("📋 Unable to determine exact Aura tier")
            logger.info("💡 Check your Neo4j Aura console for tier information")
    
    def suggest_alternatives(self):
        """Suggest alternatives for graph analytics without GDS"""
        logger.info("\n🛠️  Alternative Graph Analytics Approaches:")
        logger.info("")
        logger.info("1. 📊 Built-in Cypher Algorithms:")
        logger.info("   - Shortest path: shortestPath()")
        logger.info("   - All shortest paths: allShortestPaths()")
        logger.info("   - Basic centrality with custom Cypher")
        logger.info("")
        logger.info("2. 🐍 Python Graph Libraries:")
        logger.info("   - NetworkX: Full-featured graph analysis")
        logger.info("   - igraph: High-performance graph algorithms")
        logger.info("   - Export Neo4j data → Process with Python → Import results")
        logger.info("")
        logger.info("3. 🔄 Hybrid Approach:")
        logger.info("   - Use Neo4j for graph storage and basic queries")
        logger.info("   - Export subgraphs for complex analytics")
        logger.info("   - Store results back in Neo4j")
        logger.info("")
        logger.info("4. 📈 Custom Cypher Implementations:")
        logger.info("   - PageRank approximation with iterative Cypher")
        logger.info("   - Community detection with label propagation")
        logger.info("   - Centrality measures with path counting")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to analyze Aura instance"""
    logger.info("🚀 Analyzing Neo4j Aura Instance for GDS Capabilities...")
    logger.info("=" * 70)
    
    try:
        checker = AuraInstanceChecker()
        
        # Get instance details
        aura_tier = checker.get_instance_details()
        
        # Check available procedures
        checker.check_available_procedures()
        
        # Explain GDS options
        checker.explain_gds_options(aura_tier)
        
        # Suggest alternatives
        checker.suggest_alternatives()
        
        # Summary
        logger.info("\n" + "=" * 70)
        logger.info("📋 SUMMARY:")
        logger.info("❌ GDS Library is NOT available in your current Neo4j instance")
        logger.info("💡 Consider upgrading to AuraDS or using alternative approaches")
        logger.info("🔧 Your existing graph data can still be used for basic analytics")
        
        checker.close()
        return False  # GDS not available
        
    except Exception as e:
        logger.error(f"❌ Analysis failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
