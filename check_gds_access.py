#!/usr/bin/env python3
"""
Neo4j GDS Library Access Checker

This script checks if your Neo4j instance has access to the Graph Data Science (GDS) library
and provides detailed information about available procedures and functions.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GDSChecker:
    """Check GDS library availability and capabilities"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Connecting to Neo4j at: {self.uri}")
        logger.info(f"Username: {self.username}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def check_gds_availability(self):
        """Check if GDS library is available"""
        logger.info("\n🔍 Checking GDS Library Availability...")

        with self.driver.session() as session:
            try:
                # Try direct GDS version call first (most reliable for Aura)
                result = session.run("CALL gds.version()")
                version_info = result.single()

                if version_info:
                    logger.info("✅ GDS Library is AVAILABLE!")
                    logger.info(f"   GDS Version: {version_info['gdsVersion']}")
                    logger.info(f"   Neo4j Version: {version_info['neo4jVersion']}")
                    return True

            except Exception as e:
                logger.info(f"ℹ️  Direct GDS version check failed: {e}")

                # Fallback: Try to list GDS algorithms
                try:
                    result = session.run("CALL gds.list()")
                    algorithms = list(result)

                    if algorithms:
                        logger.info(f"✅ GDS Library is AVAILABLE! Found {len(algorithms)} algorithms")
                        return True
                    else:
                        logger.error("❌ GDS Library is NOT AVAILABLE - No algorithms found")
                        return False

                except Exception as e2:
                    logger.error(f"❌ GDS Library is NOT AVAILABLE - Error: {e2}")
                    return False
    
    def check_gds_functions(self):
        """Check available GDS functions"""
        logger.info("\n🔧 Checking GDS Functions...")

        with self.driver.session() as session:
            try:
                # For Aura, we'll check specific GDS functions directly
                test_functions = [
                    "gds.version",
                    "gds.list",
                    "gds.graph.list",
                    "gds.graph.exists"
                ]

                available_functions = []
                for func_name in test_functions:
                    try:
                        # Test if function exists by calling it
                        if func_name == "gds.version":
                            session.run(f"CALL {func_name}()")
                        elif func_name == "gds.list":
                            session.run(f"CALL {func_name}()")
                        elif func_name == "gds.graph.list":
                            session.run(f"CALL {func_name}()")
                        elif func_name == "gds.graph.exists":
                            session.run(f"RETURN {func_name}('test') as exists")

                        available_functions.append(func_name)
                    except:
                        pass  # Function not available

                if available_functions:
                    logger.info(f"✅ Found {len(available_functions)} key GDS functions:")
                    for func in available_functions:
                        logger.info(f"  🔧 {func}")
                else:
                    logger.info("ℹ️  No GDS functions accessible")

            except Exception as e:
                logger.error(f"❌ Error checking GDS functions: {e}")
    
    def check_gds_version(self):
        """Check GDS version information"""
        logger.info("\n📊 Checking GDS Version...")
        
        with self.driver.session() as session:
            try:
                result = session.run("CALL gds.version()")
                version_info = result.single()
                
                if version_info:
                    logger.info(f"✅ GDS Version: {version_info['gdsVersion']}")
                    logger.info(f"   Neo4j Version: {version_info['neo4jVersion']}")
                    logger.info(f"   Minimum Required Neo4j Version: {version_info['minimumRequiredNeo4jVersion']}")
                    logger.info(f"   Build Date: {version_info['buildDate']}")
                    logger.info(f"   Build JDK: {version_info['buildJdk']}")
                    logger.info(f"   Build Hash: {version_info['buildHash']}")
                    return True
                    
            except Exception as e:
                logger.error(f"❌ Error getting GDS version: {e}")
                return False
    
    def test_basic_gds_operations(self):
        """Test basic GDS operations"""
        logger.info("\n🧪 Testing Basic GDS Operations...")
        
        with self.driver.session() as session:
            try:
                # Test graph catalog listing
                result = session.run("CALL gds.graph.list()")
                graphs = list(result)
                logger.info(f"✅ Graph catalog accessible - Found {len(graphs)} existing graphs")
                
                # Test algorithm listing
                result = session.run("CALL gds.list()")
                algorithms = list(result)
                logger.info(f"✅ Algorithm listing accessible - Found {len(algorithms)} algorithms")
                
                # Show some key algorithms
                community_algorithms = [alg for alg in algorithms if 'community' in alg['name'].lower()]
                centrality_algorithms = [alg for alg in algorithms if 'centrality' in alg['name'].lower()]
                
                logger.info(f"   📊 Community Detection Algorithms: {len(community_algorithms)}")
                for alg in community_algorithms[:5]:  # Show first 5
                    logger.info(f"      - {alg['name']}")
                
                logger.info(f"   📊 Centrality Algorithms: {len(centrality_algorithms)}")
                for alg in centrality_algorithms[:5]:  # Show first 5
                    logger.info(f"      - {alg['name']}")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Error testing basic GDS operations: {e}")
                return False
    
    def check_database_info(self):
        """Check database information"""
        logger.info("\n💾 Checking Database Information...")
        
        with self.driver.session() as session:
            try:
                # Check Neo4j version
                result = session.run("CALL dbms.components()")
                components = list(result)
                
                for component in components:
                    if component['name'] == 'Neo4j Kernel':
                        logger.info(f"✅ Neo4j Version: {component['versions'][0]}")
                        logger.info(f"   Edition: {component['edition']}")
                
                # Check database size
                result = session.run("MATCH (n) RETURN count(n) as node_count")
                node_count = result.single()['node_count']
                
                result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
                rel_count = result.single()['rel_count']
                
                logger.info(f"📊 Database Size:")
                logger.info(f"   Nodes: {node_count:,}")
                logger.info(f"   Relationships: {rel_count:,}")
                
            except Exception as e:
                logger.error(f"❌ Error checking database info: {e}")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to run all GDS checks"""
    logger.info("🚀 Starting Neo4j GDS Library Access Check...")
    logger.info("=" * 60)
    
    try:
        checker = GDSChecker()
        
        # Run all checks
        gds_available = checker.check_gds_availability()
        
        if gds_available:
            checker.check_gds_functions()
            checker.check_gds_version()
            checker.test_basic_gds_operations()
        
        checker.check_database_info()
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📋 SUMMARY:")
        
        if gds_available:
            logger.info("✅ GDS Library is AVAILABLE and functional")
            logger.info("💡 You can use all GDS community detection features")
            logger.info("🎯 Ready for advanced graph analytics!")
        else:
            logger.info("❌ GDS Library is NOT AVAILABLE")
            logger.info("💡 You may need to:")
            logger.info("   1. Upgrade to Neo4j Enterprise Edition")
            logger.info("   2. Install the GDS plugin")
            logger.info("   3. Check your Neo4j Aura instance configuration")
        
        checker.close()
        return gds_available
        
    except Exception as e:
        logger.error(f"❌ GDS check failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
