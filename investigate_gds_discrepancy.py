#!/usr/bin/env python3
"""
Investigate GDS Discrepancy

This script investigates why GDS procedures are listed but not callable.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GDSDiscrepancyInvestigator:
    """Investigate the GDS procedure discrepancy"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Investigating GDS at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def list_all_procedures(self):
        """List all procedures to see what's actually available"""
        logger.info("\n🔍 Listing All Available Procedures...")
        
        with self.driver.session() as session:
            try:
                result = session.run("SHOW PROCEDURES")
                procedures = list(result)
                
                logger.info(f"📊 Total procedures found: {len(procedures)}")
                
                # Filter for GDS procedures
                gds_procedures = [p for p in procedures if 'gds' in p['name'].lower()]
                apoc_procedures = [p for p in procedures if 'apoc' in p['name'].lower()]
                
                logger.info(f"   GDS procedures: {len(gds_procedures)}")
                logger.info(f"   APOC procedures: {len(apoc_procedures)}")
                
                if gds_procedures:
                    logger.info("\n📋 First 10 GDS procedures found:")
                    for i, proc in enumerate(gds_procedures[:10]):
                        logger.info(f"   {i+1}. {proc['name']}")
                        logger.info(f"      Description: {proc['description'][:80]}...")
                        logger.info(f"      Mode: {proc['mode']}")
                        logger.info("")
                    
                    # Test calling one of the listed procedures
                    logger.info("🧪 Testing to call a listed GDS procedure...")
                    test_proc = gds_procedures[0]['name']
                    logger.info(f"   Trying to call: {test_proc}")
                    
                    try:
                        if 'estimate' in test_proc:
                            # Skip estimate procedures as they need parameters
                            logger.info("   ⏭️  Skipping estimate procedure (needs parameters)")
                        else:
                            result = session.run(f"CALL {test_proc}()")
                            data = list(result)
                            logger.info(f"   ✅ SUCCESS: Procedure callable, returned {len(data)} records")
                    except Exception as e:
                        logger.error(f"   ❌ FAILED to call {test_proc}: {e}")
                
                return gds_procedures
                
            except Exception as e:
                logger.error(f"❌ Error listing procedures: {e}")
                return []
    
    def test_specific_gds_procedures(self, gds_procedures):
        """Test specific GDS procedures that should work"""
        logger.info("\n🎯 Testing Specific GDS Procedures...")
        
        # Look for procedures that don't need parameters
        simple_procedures = [
            'gds.version',
            'gds.list',
            'gds.graph.list'
        ]
        
        with self.driver.session() as session:
            for proc_name in simple_procedures:
                logger.info(f"🔍 Testing {proc_name}...")
                
                # Check if this procedure is in our list
                matching_procs = [p for p in gds_procedures if p['name'] == proc_name]
                
                if matching_procs:
                    logger.info(f"   ✅ Procedure {proc_name} is listed")
                    proc_info = matching_procs[0]
                    logger.info(f"      Mode: {proc_info['mode']}")
                    logger.info(f"      Signature: {proc_info['signature']}")
                    
                    try:
                        result = session.run(f"CALL {proc_name}()")
                        data = list(result)
                        logger.info(f"   ✅ SUCCESS: {proc_name} returned {len(data)} records")
                        
                        if proc_name == 'gds.version' and data:
                            version_info = data[0]
                            logger.info(f"      GDS Version: {version_info.get('gdsVersion', 'N/A')}")
                        elif proc_name == 'gds.list' and data:
                            logger.info(f"      Found {len(data)} algorithms")
                        elif proc_name == 'gds.graph.list' and data:
                            logger.info(f"      Found {len(data)} graph projections")
                            
                    except Exception as e:
                        logger.error(f"   ❌ FAILED to call {proc_name}: {e}")
                else:
                    logger.info(f"   ❌ Procedure {proc_name} is NOT listed")
    
    def check_database_edition_and_version(self):
        """Check database edition and version details"""
        logger.info("\n💾 Checking Database Edition and Version...")
        
        with self.driver.session() as session:
            try:
                # Get Neo4j components
                result = session.run("CALL dbms.components()")
                components = list(result)
                
                for component in components:
                    logger.info(f"📊 {component['name']}")
                    logger.info(f"   Version: {component['versions'][0]}")
                    logger.info(f"   Edition: {component['edition']}")
                
                # Check if this is truly AuraDB Professional
                if any('aura' in comp['versions'][0].lower() for comp in components):
                    logger.info("✅ Confirmed: This is a Neo4j Aura instance")
                    
                    # Check for any GDS-related configuration
                    try:
                        result = session.run("CALL dbms.listConfig() YIELD name, value WHERE name CONTAINS 'gds'")
                        gds_config = list(result)
                        
                        if gds_config:
                            logger.info(f"🔧 Found {len(gds_config)} GDS-related configuration items:")
                            for config in gds_config:
                                logger.info(f"   {config['name']}: {config['value']}")
                        else:
                            logger.info("ℹ️  No GDS-related configuration found")
                            
                    except Exception as e:
                        logger.info(f"ℹ️  Cannot access configuration: {e}")
                
            except Exception as e:
                logger.error(f"❌ Error checking database info: {e}")
    
    def test_alternative_gds_access(self):
        """Test alternative ways to access GDS functionality"""
        logger.info("\n🔄 Testing Alternative GDS Access Methods...")
        
        with self.driver.session() as session:
            # Test 1: Try calling procedures with full namespace
            alternative_calls = [
                "CALL gds.graph.project.cypher",
                "CALL gds.pageRank.stream",
                "CALL gds.louvain.stream"
            ]
            
            for call in alternative_calls:
                logger.info(f"🔍 Testing {call}...")
                try:
                    # Just test if the procedure exists by calling EXPLAIN
                    result = session.run(f"EXPLAIN {call}({{nodeQuery: 'MATCH (n) RETURN id(n) as id', relationshipQuery: 'MATCH (a)-[r]->(b) RETURN id(a) as source, id(b) as target'}})")
                    logger.info(f"   ✅ Procedure {call} exists (EXPLAIN worked)")
                except Exception as e:
                    if "ProcedureNotFound" in str(e):
                        logger.info(f"   ❌ Procedure {call} not found")
                    else:
                        logger.info(f"   ⚠️  Procedure {call} exists but has parameter issues: {e}")
    
    def final_assessment(self, gds_procedures):
        """Provide final assessment of GDS availability"""
        logger.info("\n📋 FINAL ASSESSMENT:")
        logger.info("=" * 50)
        
        if gds_procedures:
            logger.info(f"✅ GDS procedures are LISTED ({len(gds_procedures)} found)")
            
            # Check if any core procedures are actually callable
            core_procedures = ['gds.version', 'gds.list', 'gds.graph.list']
            callable_procedures = []
            
            with self.driver.session() as session:
                for proc_name in core_procedures:
                    try:
                        session.run(f"CALL {proc_name}()")
                        callable_procedures.append(proc_name)
                    except:
                        pass
            
            if callable_procedures:
                logger.info(f"✅ GDS procedures are CALLABLE ({len(callable_procedures)} tested successfully)")
                logger.info("🎉 GDS is FUNCTIONAL in your AuraDB Professional instance!")
                logger.info("💡 You can use GDS algorithms via CALL procedures")
            else:
                logger.info("❌ GDS procedures are NOT CALLABLE")
                logger.info("💡 This suggests:")
                logger.info("   1. GDS may be listed but not enabled")
                logger.info("   2. Your AuraDB Professional plan may not include GDS")
                logger.info("   3. GDS may require additional activation")
                logger.info("")
                logger.info("🔧 Recommended actions:")
                logger.info("   1. Contact Neo4j support about GDS availability")
                logger.info("   2. Check your AuraDB Professional plan details")
                logger.info("   3. Consider upgrading to AuraDS for guaranteed GDS access")
        else:
            logger.info("❌ GDS procedures are NOT LISTED")
            logger.info("💡 GDS is not available in your current instance")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to investigate GDS discrepancy"""
    logger.info("🚀 Investigating GDS Procedure Discrepancy...")
    logger.info("=" * 60)
    
    try:
        investigator = GDSDiscrepancyInvestigator()
        
        # List all procedures
        gds_procedures = investigator.list_all_procedures()
        
        if gds_procedures:
            # Test specific procedures
            investigator.test_specific_gds_procedures(gds_procedures)
            
            # Test alternative access methods
            investigator.test_alternative_gds_access()
        
        # Check database details
        investigator.check_database_edition_and_version()
        
        # Final assessment
        investigator.final_assessment(gds_procedures)
        
        investigator.close()
        return len(gds_procedures) > 0
        
    except Exception as e:
        logger.error(f"❌ Investigation failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
