#!/usr/bin/env python3
"""
Neo4j GDS Aura Credentials Setup

This script helps you set up GDS credentials for Neo4j Aura instances.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging
import getpass

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GDSCredentialsSetup:
    """Set up GDS credentials for Aura"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Connecting to Neo4j at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def explain_gds_credentials(self):
        """Explain what GDS credentials are and how to get them"""
        logger.info("\n📋 Neo4j GDS Aura Credentials Information:")
        logger.info("=" * 60)
        logger.info("")
        logger.info("🔑 What are GDS Aura Credentials?")
        logger.info("   - Special API credentials required for GDS operations in Aura")
        logger.info("   - Different from your regular Neo4j database credentials")
        logger.info("   - Provided by Neo4j for GDS-enabled Aura instances")
        logger.info("")
        logger.info("📍 How to get GDS Credentials:")
        logger.info("   1. Log into your Neo4j Aura Console")
        logger.info("   2. Navigate to your database instance")
        logger.info("   3. Look for 'GDS' or 'Graph Data Science' section")
        logger.info("   4. Generate or retrieve API credentials")
        logger.info("   5. You'll get a Client ID and Client Secret")
        logger.info("")
        logger.info("⚠️  Important Notes:")
        logger.info("   - GDS may require a paid Aura plan")
        logger.info("   - Not all Aura instances have GDS enabled")
        logger.info("   - Credentials are instance-specific")
        logger.info("")
    
    def check_current_credentials(self):
        """Check if GDS credentials are already set"""
        logger.info("🔍 Checking current GDS credential status...")
        
        with self.driver.session() as session:
            try:
                # Try a simple GDS operation to see if credentials are set
                result = session.run("CALL gds.graph.list()")
                graphs = list(result)
                logger.info(f"✅ GDS credentials are already configured!")
                logger.info(f"   Found {len(graphs)} graph projections")
                return True
                
            except Exception as e:
                error_msg = str(e)
                if "AuraApiCredentialsMissing" in error_msg or "No Aura API credentials" in error_msg:
                    logger.info("❌ GDS credentials are NOT configured")
                    return False
                else:
                    logger.error(f"❌ Error checking credentials: {e}")
                    return False
    
    def setup_credentials_interactive(self):
        """Interactive setup of GDS credentials"""
        logger.info("\n🔧 Interactive GDS Credentials Setup:")
        logger.info("=" * 50)
        
        print("\nTo set up GDS credentials, you need:")
        print("1. Client ID (from Neo4j Aura Console)")
        print("2. Client Secret (from Neo4j Aura Console)")
        print("")
        
        # Get credentials from user
        client_id = input("Enter your GDS Client ID: ").strip()
        if not client_id:
            logger.error("❌ Client ID is required")
            return False
        
        client_secret = getpass.getpass("Enter your GDS Client Secret: ").strip()
        if not client_secret:
            logger.error("❌ Client Secret is required")
            return False
        
        return self.set_credentials(client_id, client_secret)
    
    def set_credentials(self, client_id, client_secret):
        """Set GDS credentials in the database"""
        logger.info("\n🔐 Setting GDS credentials...")
        
        with self.driver.session() as session:
            try:
                # Set the credentials
                session.run("""
                CALL gds.aura.api.credentials($clientId, $clientSecret)
                """, clientId=client_id, clientSecret=client_secret)
                
                logger.info("✅ GDS credentials set successfully!")
                
                # Test the credentials
                result = session.run("CALL gds.graph.list()")
                graphs = list(result)
                logger.info(f"✅ Credentials verified - Found {len(graphs)} graph projections")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to set credentials: {e}")
                return False
    
    def test_gds_functionality(self):
        """Test basic GDS functionality after credentials are set"""
        logger.info("\n🧪 Testing GDS functionality...")
        
        with self.driver.session() as session:
            try:
                # Test gds.version
                result = session.run("CALL gds.version()")
                version_info = result.single()
                logger.info(f"✅ GDS Version: {version_info['gdsVersion']}")
                
                # Test gds.list
                result = session.run("CALL gds.list()")
                algorithms = list(result)
                logger.info(f"✅ Available algorithms: {len(algorithms)}")
                
                # Show some key algorithms
                community_algs = [alg for alg in algorithms if 'community' in alg['name'].lower()]
                centrality_algs = [alg for alg in algorithms if 'centrality' in alg['name'].lower()]
                
                logger.info(f"   📊 Community Detection: {len(community_algs)} algorithms")
                logger.info(f"   📊 Centrality: {len(centrality_algs)} algorithms")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ GDS functionality test failed: {e}")
                return False
    
    def save_credentials_to_env(self, client_id, client_secret):
        """Save credentials to .env file for future use"""
        logger.info("\n💾 Saving credentials to .env file...")
        
        try:
            # Read existing .env file
            env_path = ".env"
            env_lines = []
            
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    env_lines = f.readlines()
            
            # Remove existing GDS credential lines
            env_lines = [line for line in env_lines if not line.startswith('GDS_CLIENT_ID=') and not line.startswith('GDS_CLIENT_SECRET=')]
            
            # Add new credentials
            env_lines.append(f"GDS_CLIENT_ID={client_id}\n")
            env_lines.append(f"GDS_CLIENT_SECRET={client_secret}\n")
            
            # Write back to file
            with open(env_path, 'w') as f:
                f.writelines(env_lines)
            
            logger.info("✅ Credentials saved to .env file")
            logger.info("💡 You can now use these credentials in your applications")
            
        except Exception as e:
            logger.error(f"❌ Failed to save credentials: {e}")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to set up GDS credentials"""
    logger.info("🚀 Neo4j GDS Aura Credentials Setup")
    logger.info("=" * 50)
    
    try:
        setup = GDSCredentialsSetup()
        
        # Explain what GDS credentials are
        setup.explain_gds_credentials()
        
        # Check if credentials are already set
        if setup.check_current_credentials():
            logger.info("\n🎉 GDS is already configured and working!")
            setup.test_gds_functionality()
            setup.close()
            return True
        
        # Interactive setup
        print("\n" + "=" * 60)
        print("GDS CREDENTIALS SETUP REQUIRED")
        print("=" * 60)
        
        choice = input("\nDo you have GDS API credentials from Neo4j Aura Console? (y/n): ").lower().strip()
        
        if choice == 'y':
            success = setup.setup_credentials_interactive()
            
            if success:
                # Test functionality
                setup.test_gds_functionality()
                
                # Ask if user wants to save credentials
                save_choice = input("\nSave credentials to .env file for future use? (y/n): ").lower().strip()
                if save_choice == 'y':
                    client_id = input("Re-enter Client ID: ").strip()
                    client_secret = getpass.getpass("Re-enter Client Secret: ").strip()
                    setup.save_credentials_to_env(client_id, client_secret)
                
                logger.info("\n🎉 GDS setup completed successfully!")
                logger.info("💡 You can now use all GDS features including community detection")
            else:
                logger.error("\n❌ GDS setup failed")
                logger.error("💡 Please check your credentials and try again")
        else:
            logger.info("\n📋 Next Steps:")
            logger.info("1. Log into your Neo4j Aura Console")
            logger.info("2. Navigate to your database instance")
            logger.info("3. Look for GDS/Graph Data Science section")
            logger.info("4. Generate API credentials")
            logger.info("5. Run this script again with your credentials")
        
        setup.close()
        return success if choice == 'y' else False
        
    except Exception as e:
        logger.error(f"❌ GDS setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
