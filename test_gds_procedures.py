#!/usr/bin/env python3
"""
Test GDS Procedures in AuraDB Professional

This script tests GDS using CALL procedures instead of functions,
which is the correct approach for AuraDB Professional.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GDSProcedureTest:
    """Test GDS using procedures (CALL statements)"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Testing GDS Procedures at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def test_gds_procedures(self):
        """Test GDS procedures step by step"""
        logger.info("\n🧪 Testing GDS Procedures...")
        
        with self.driver.session() as session:
            # Test 1: Try gds.version procedure
            logger.info("🔍 Testing CALL gds.version()...")
            try:
                result = session.run("CALL gds.version()")
                version_info = result.single()
                logger.info(f"   ✅ SUCCESS: GDS Version {version_info['gdsVersion']}")
                logger.info(f"   Neo4j Version: {version_info['neo4jVersion']}")
                return True
            except Exception as e:
                logger.error(f"   ❌ FAILED: {e}")
                
                # Test 2: Try gds.list procedure
                logger.info("🔍 Testing CALL gds.list()...")
                try:
                    result = session.run("CALL gds.list()")
                    algorithms = list(result)
                    logger.info(f"   ✅ SUCCESS: Found {len(algorithms)} algorithms")
                    
                    # Show some algorithms
                    for i, alg in enumerate(algorithms[:5]):
                        logger.info(f"      {i+1}. {alg['name']}")
                    
                    return True
                except Exception as e2:
                    logger.error(f"   ❌ FAILED: {e2}")
                    return False
    
    def test_simple_algorithm(self):
        """Test a simple GDS algorithm with minimal data"""
        logger.info("\n🔬 Testing Simple GDS Algorithm...")
        
        with self.driver.session() as session:
            try:
                # Create minimal test data
                session.run("""
                MERGE (a:SimpleTest {id: 1})
                MERGE (b:SimpleTest {id: 2})
                MERGE (c:SimpleTest {id: 3})
                MERGE (a)-[:LINKS]->(b)
                MERGE (b)-[:LINKS]->(c)
                MERGE (c)-[:LINKS]->(a)
                """)
                logger.info("   📊 Created simple test data")
                
                # Test PageRank with stream mode (no graph projection needed)
                logger.info("🔍 Testing PageRank stream...")
                try:
                    result = session.run("""
                    CALL gds.pageRank.stream({
                        nodeProjection: 'SimpleTest',
                        relationshipProjection: 'LINKS'
                    })
                    YIELD nodeId, score
                    RETURN gds.util.asNode(nodeId).id as id, score
                    ORDER BY score DESC
                    """)
                    
                    pagerank_results = list(result)
                    if pagerank_results:
                        logger.info(f"   ✅ SUCCESS: PageRank completed")
                        for node in pagerank_results:
                            logger.info(f"      Node {node['id']}: {node['score']:.4f}")
                        success = True
                    else:
                        logger.info("   ❌ No PageRank results")
                        success = False
                        
                except Exception as e:
                    logger.error(f"   ❌ PageRank FAILED: {e}")
                    success = False
                
                # Clean up
                session.run("MATCH (n:SimpleTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return success
                
            except Exception as e:
                logger.error(f"❌ Error in algorithm test: {e}")
                return False
    
    def test_graph_projection_approach(self):
        """Test the graph projection approach"""
        logger.info("\n🏗️  Testing Graph Projection Approach...")
        
        with self.driver.session() as session:
            try:
                # Create test data
                session.run("""
                MERGE (a:ProjectionTest {id: 1, name: 'A'})
                MERGE (b:ProjectionTest {id: 2, name: 'B'})
                MERGE (c:ProjectionTest {id: 3, name: 'C'})
                MERGE (a)-[:CONNECTS]->(b)
                MERGE (b)-[:CONNECTS]->(c)
                MERGE (c)-[:CONNECTS]->(a)
                """)
                logger.info("   📊 Created projection test data")
                
                # Try to create a graph projection
                logger.info("🔍 Testing graph projection creation...")
                try:
                    result = session.run("""
                    CALL gds.graph.project(
                        'test_graph',
                        'ProjectionTest',
                        'CONNECTS'
                    )
                    """)
                    
                    projection_info = result.single()
                    logger.info(f"   ✅ SUCCESS: Graph projection created")
                    logger.info(f"      Nodes: {projection_info['nodeCount']}")
                    logger.info(f"      Relationships: {projection_info['relationshipCount']}")
                    
                    # Run algorithm on projected graph
                    logger.info("🔍 Testing algorithm on projected graph...")
                    result = session.run("""
                    CALL gds.pageRank.stream('test_graph')
                    YIELD nodeId, score
                    RETURN gds.util.asNode(nodeId).name as name, score
                    ORDER BY score DESC
                    """)
                    
                    results = list(result)
                    if results:
                        logger.info(f"   ✅ SUCCESS: Algorithm on projection worked")
                        for node in results:
                            logger.info(f"      {node['name']}: {node['score']:.4f}")
                    
                    # Clean up projection
                    session.run("CALL gds.graph.drop('test_graph')")
                    logger.info("   🧹 Dropped graph projection")
                    
                    success = True
                    
                except Exception as e:
                    logger.error(f"   ❌ Graph projection FAILED: {e}")
                    success = False
                
                # Clean up data
                session.run("MATCH (n:ProjectionTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return success
                
            except Exception as e:
                logger.error(f"❌ Error in projection test: {e}")
                return False
    
    def check_available_algorithms(self):
        """Check what GDS algorithms are actually available"""
        logger.info("\n📋 Checking Available GDS Algorithms...")
        
        with self.driver.session() as session:
            try:
                result = session.run("CALL gds.list()")
                algorithms = list(result)
                
                if algorithms:
                    logger.info(f"✅ Found {len(algorithms)} GDS algorithms:")
                    
                    # Categorize algorithms
                    categories = {}
                    for alg in algorithms:
                        name = alg['name']
                        if 'community' in name.lower():
                            categories.setdefault('Community Detection', []).append(name)
                        elif 'centrality' in name.lower() or 'pagerank' in name.lower():
                            categories.setdefault('Centrality', []).append(name)
                        elif 'path' in name.lower() or 'shortest' in name.lower():
                            categories.setdefault('Path Finding', []).append(name)
                        else:
                            categories.setdefault('Other', []).append(name)
                    
                    for category, algs in categories.items():
                        logger.info(f"   📊 {category}: {len(algs)} algorithms")
                        for alg in algs[:3]:  # Show first 3 in each category
                            logger.info(f"      - {alg}")
                        if len(algs) > 3:
                            logger.info(f"      ... and {len(algs) - 3} more")
                    
                    return True
                else:
                    logger.info("❌ No algorithms found")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Error listing algorithms: {e}")
                return False
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to test GDS procedures"""
    logger.info("🚀 Testing GDS Procedures in AuraDB Professional...")
    logger.info("=" * 70)
    
    try:
        tester = GDSProcedureTest()
        
        # Test basic procedures
        procedures_work = tester.test_gds_procedures()
        
        if procedures_work:
            # Check available algorithms
            algorithms_available = tester.check_available_algorithms()
            
            # Test simple algorithm
            simple_algorithm_works = tester.test_simple_algorithm()
            
            # Test graph projection approach
            projection_works = tester.test_graph_projection_approach()
        else:
            algorithms_available = False
            simple_algorithm_works = False
            projection_works = False
        
        # Summary
        logger.info("\n" + "=" * 70)
        logger.info("📋 GDS PROCEDURES TEST RESULTS:")
        
        logger.info(f"   Basic Procedures: {'✅' if procedures_work else '❌'}")
        logger.info(f"   Algorithm Listing: {'✅' if algorithms_available else '❌'}")
        logger.info(f"   Simple Algorithm: {'✅' if simple_algorithm_works else '❌'}")
        logger.info(f"   Graph Projection: {'✅' if projection_works else '❌'}")
        
        if procedures_work and (simple_algorithm_works or projection_works):
            logger.info("\n🎉 GDS is FUNCTIONAL in AuraDB Professional!")
            logger.info("💡 Use CALL procedures for GDS operations")
            logger.info("🔧 Your existing GDS code needs to use procedures, not functions")
            
            if projection_works:
                logger.info("✅ Graph projection approach works - recommended for complex analysis")
            if simple_algorithm_works:
                logger.info("✅ Direct algorithm calls work - good for simple operations")
                
        elif procedures_work:
            logger.info("\n⚠️  GDS is PARTIALLY FUNCTIONAL")
            logger.info("💡 Basic procedures work but algorithms may have issues")
        else:
            logger.info("\n❌ GDS is NOT FUNCTIONAL")
            logger.info("💡 GDS may not be enabled in your AuraDB Professional instance")
        
        tester.close()
        return procedures_work and (simple_algorithm_works or projection_works)
        
    except Exception as e:
        logger.error(f"❌ GDS procedures test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
