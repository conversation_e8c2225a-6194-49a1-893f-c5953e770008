#!/usr/bin/env python3
"""
AuraDB Professional GDS Test

This script tests GDS functionality in AuraDB Professional using direct Cypher queries
and the graphdatascience Python client with Bolt credentials.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AuraDBProfessionalGDSTest:
    """Test GDS functionality in AuraDB Professional"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Testing AuraDB Professional GDS at: {self.uri}")
        logger.info(f"Username: {self.username}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def test_direct_gds_calls(self):
        """Test GDS algorithms via direct Cypher queries"""
        logger.info("\n🧪 Testing Direct GDS Cypher Queries...")
        
        with self.driver.session() as session:
            try:
                # Test 1: Check GDS version (should work without API credentials in Professional)
                logger.info("🔍 Testing gds.version()...")
                try:
                    result = session.run("RETURN gds.version() as version")
                    version_info = result.single()
                    if version_info and version_info['version']:
                        logger.info(f"   ✅ SUCCESS: GDS Version {version_info['version']}")
                        return True
                    else:
                        logger.info("   ❌ No version info returned")
                        return False
                except Exception as e:
                    logger.error(f"   ❌ FAILED: {e}")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ Error in direct GDS test: {e}")
                return False
    
    def test_gds_algorithms_direct(self):
        """Test GDS algorithms directly without graph projection"""
        logger.info("\n🔬 Testing Direct GDS Algorithm Calls...")
        
        with self.driver.session() as session:
            try:
                # Create simple test data
                session.run("""
                MERGE (a:TestNode {id: 1, name: 'A'})
                MERGE (b:TestNode {id: 2, name: 'B'})
                MERGE (c:TestNode {id: 3, name: 'C'})
                MERGE (d:TestNode {id: 4, name: 'D'})
                MERGE (a)-[:CONNECTS]->(b)
                MERGE (b)-[:CONNECTS]->(c)
                MERGE (c)-[:CONNECTS]->(d)
                MERGE (d)-[:CONNECTS]->(a)
                MERGE (a)-[:CONNECTS]->(c)
                """)
                logger.info("   📊 Created test data")
                
                # Test PageRank algorithm
                logger.info("🔍 Testing PageRank algorithm...")
                try:
                    result = session.run("""
                    CALL gds.pageRank.stream({
                        nodeProjection: 'TestNode',
                        relationshipProjection: 'CONNECTS'
                    })
                    YIELD nodeId, score
                    RETURN gds.util.asNode(nodeId).name as name, score
                    ORDER BY score DESC
                    """)
                    
                    pagerank_results = list(result)
                    if pagerank_results:
                        logger.info(f"   ✅ SUCCESS: PageRank completed for {len(pagerank_results)} nodes")
                        for node in pagerank_results:
                            logger.info(f"      {node['name']}: {node['score']:.4f}")
                    else:
                        logger.info("   ❌ No PageRank results")
                        
                except Exception as e:
                    logger.error(f"   ❌ PageRank FAILED: {e}")
                
                # Test Louvain community detection
                logger.info("🔍 Testing Louvain community detection...")
                try:
                    result = session.run("""
                    CALL gds.louvain.stream({
                        nodeProjection: 'TestNode',
                        relationshipProjection: 'CONNECTS'
                    })
                    YIELD nodeId, communityId
                    RETURN gds.util.asNode(nodeId).name as name, communityId
                    ORDER BY communityId, name
                    """)
                    
                    community_results = list(result)
                    if community_results:
                        logger.info(f"   ✅ SUCCESS: Louvain completed for {len(community_results)} nodes")
                        for node in community_results:
                            logger.info(f"      {node['name']}: Community {node['communityId']}")
                    else:
                        logger.info("   ❌ No Louvain results")
                        
                except Exception as e:
                    logger.error(f"   ❌ Louvain FAILED: {e}")
                
                # Clean up test data
                session.run("MATCH (n:TestNode) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return len(pagerank_results) > 0 or len(community_results) > 0
                
            except Exception as e:
                logger.error(f"❌ Error in algorithm test: {e}")
                return False
    
    def test_graphdatascience_client(self):
        """Test the graphdatascience Python client"""
        logger.info("\n🐍 Testing graphdatascience Python Client...")
        
        try:
            # Try to import the graphdatascience client
            from graphdatascience import GraphDataScience
            logger.info("   ✅ graphdatascience package is available")
            
            # Initialize GDS client with Bolt credentials
            gds = GraphDataScience(self.uri, auth=(self.username, self.password))
            
            # Test connection
            version = gds.version()
            logger.info(f"   ✅ GDS Client connected - Version: {version}")
            
            # Test listing algorithms
            algorithms = gds.list()
            logger.info(f"   ✅ Found {len(algorithms)} algorithms via Python client")
            
            # Show some key algorithms
            community_algs = [alg for alg in algorithms if 'community' in alg['name'].lower()]
            centrality_algs = [alg for alg in algorithms if 'centrality' in alg['name'].lower()]
            
            logger.info(f"      📊 Community Detection: {len(community_algs)} algorithms")
            logger.info(f"      📊 Centrality: {len(centrality_algs)} algorithms")
            
            gds.close()
            return True
            
        except ImportError:
            logger.info("   ℹ️  graphdatascience package not installed")
            logger.info("   💡 Install with: pip install graphdatascience")
            return False
            
        except Exception as e:
            logger.error(f"   ❌ GDS Client test failed: {e}")
            return False
    
    def test_existing_graph_data(self):
        """Test GDS on your existing legal case data"""
        logger.info("\n⚖️  Testing GDS on Existing Legal Data...")
        
        with self.driver.session() as session:
            try:
                # Check what data exists
                result = session.run("""
                MATCH (n) 
                RETURN labels(n)[0] as label, count(*) as count 
                ORDER BY count DESC 
                LIMIT 10
                """)
                
                data_summary = list(result)
                logger.info("   📊 Existing data summary:")
                for item in data_summary:
                    logger.info(f"      {item['label']}: {item['count']} nodes")
                
                # Look for case nodes specifically
                result = session.run("""
                MATCH (c:Case) 
                RETURN count(c) as case_count
                """)
                case_count = result.single()['case_count']
                
                if case_count > 0:
                    logger.info(f"   ✅ Found {case_count} Case nodes")
                    
                    # Test PageRank on actual case data
                    logger.info("🔍 Testing PageRank on legal cases...")
                    try:
                        result = session.run("""
                        CALL gds.pageRank.stream({
                            nodeProjection: 'Case',
                            relationshipProjection: {
                                CITES: {orientation: 'NATURAL'},
                                RELATES_TO: {orientation: 'UNDIRECTED'}
                            }
                        })
                        YIELD nodeId, score
                        RETURN gds.util.asNode(nodeId).title as title, score
                        ORDER BY score DESC
                        LIMIT 5
                        """)
                        
                        top_cases = list(result)
                        if top_cases:
                            logger.info(f"   ✅ SUCCESS: Top {len(top_cases)} cases by PageRank:")
                            for case in top_cases:
                                title = case['title'][:60] + "..." if len(case['title']) > 60 else case['title']
                                logger.info(f"      {title}: {case['score']:.4f}")
                        else:
                            logger.info("   ℹ️  No PageRank results (may need relationships)")
                            
                    except Exception as e:
                        logger.error(f"   ❌ PageRank on cases failed: {e}")
                
                else:
                    logger.info("   ℹ️  No Case nodes found - using general node analysis")
                
                return case_count > 0
                
            except Exception as e:
                logger.error(f"❌ Error testing existing data: {e}")
                return False
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to test AuraDB Professional GDS"""
    logger.info("🚀 Testing AuraDB Professional GDS Capabilities...")
    logger.info("=" * 70)
    
    try:
        tester = AuraDBProfessionalGDSTest()
        
        # Test direct GDS calls
        direct_success = tester.test_direct_gds_calls()
        
        if direct_success:
            # Test algorithms
            algorithm_success = tester.test_gds_algorithms_direct()
            
            # Test Python client
            client_success = tester.test_graphdatascience_client()
            
            # Test on existing data
            data_success = tester.test_existing_graph_data()
        else:
            algorithm_success = False
            client_success = False
            data_success = False
        
        # Summary
        logger.info("\n" + "=" * 70)
        logger.info("📋 AURADB PROFESSIONAL GDS TEST RESULTS:")
        
        logger.info(f"   Direct GDS Calls: {'✅' if direct_success else '❌'}")
        logger.info(f"   Algorithm Execution: {'✅' if algorithm_success else '❌'}")
        logger.info(f"   Python Client: {'✅' if client_success else '❌'}")
        logger.info(f"   Existing Data Analysis: {'✅' if data_success else '❌'}")
        
        if direct_success and algorithm_success:
            logger.info("\n🎉 GDS is FULLY FUNCTIONAL in AuraDB Professional!")
            logger.info("💡 You can use GDS algorithms directly via Cypher queries")
            logger.info("🔧 Your existing GDS integration code should work")
            
            if client_success:
                logger.info("🐍 Python graphdatascience client is also available")
            else:
                logger.info("💡 Consider installing: pip install graphdatascience")
                
        elif direct_success:
            logger.info("\n⚠️  GDS is PARTIALLY FUNCTIONAL")
            logger.info("💡 Basic functions work but some algorithms may have issues")
        else:
            logger.info("\n❌ GDS is NOT FUNCTIONAL")
            logger.info("💡 May need to check AuraDB Professional configuration")
        
        tester.close()
        return direct_success and algorithm_success
        
    except Exception as e:
        logger.error(f"❌ AuraDB Professional GDS test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
