#!/usr/bin/env python3
"""
Setup Aura GDS Credentials

This script helps you configure GDS credentials for your AuraDB Professional instance.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging
import getpass

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AuraGDSCredentialSetup:
    """Set up GDS credentials for AuraDB Professional"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Setting up GDS credentials for: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def setup_credentials_interactive(self):
        """Interactive setup of GDS credentials"""
        logger.info("\n🔧 Setting up GDS Credentials for AuraDB Professional")
        logger.info("=" * 60)
        
        print("\nYou mentioned you have GDS API credentials!")
        print("Please provide your:")
        print("1. Client ID")
        print("2. Client Secret")
        print("")
        
        # Get credentials from user
        client_id = input("Enter your GDS Client ID: ").strip()
        if not client_id:
            logger.error("❌ Client ID is required")
            return False
        
        client_secret = getpass.getpass("Enter your GDS Client Secret: ").strip()
        if not client_secret:
            logger.error("❌ Client Secret is required")
            return False
        
        return self.configure_credentials(client_id, client_secret)
    
    def configure_credentials(self, client_id, client_secret):
        """Configure GDS credentials in the database"""
        logger.info("\n🔐 Configuring GDS credentials...")
        
        with self.driver.session() as session:
            try:
                # Set the credentials using the procedure we know exists
                session.run("""
                CALL gds.aura.api.credentials($clientId, $clientSecret)
                """, clientId=client_id, clientSecret=client_secret)
                
                logger.info("✅ GDS credentials configured successfully!")
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to configure credentials: {e}")
                return False
    
    def test_gds_functionality(self):
        """Test GDS functionality after credentials are configured"""
        logger.info("\n🧪 Testing GDS functionality...")
        
        with self.driver.session() as session:
            try:
                # Test 1: Graph list (should work now)
                logger.info("🔍 Testing gds.graph.list...")
                result = session.run("CALL gds.graph.list()")
                graphs = list(result)
                logger.info(f"   ✅ SUCCESS: Found {len(graphs)} graph projections")
                
                # Test 2: Try to find gds.version or similar
                logger.info("🔍 Looking for GDS version info...")
                try:
                    # Try different ways to get version
                    result = session.run("CALL gds.debug.sysInfo()")
                    info = result.single()
                    if info:
                        logger.info(f"   ✅ GDS system info accessible")
                except:
                    logger.info("   ℹ️  GDS version info not available (normal)")
                
                # Test 3: Simple algorithm test
                logger.info("🔍 Testing simple PageRank algorithm...")
                
                # Create minimal test data
                session.run("""
                MERGE (a:GDSTest {id: 1, name: 'A'})
                MERGE (b:GDSTest {id: 2, name: 'B'})
                MERGE (c:GDSTest {id: 3, name: 'C'})
                MERGE (a)-[:LINKS]->(b)
                MERGE (b)-[:LINKS]->(c)
                MERGE (c)-[:LINKS]->(a)
                """)
                
                # Test PageRank
                result = session.run("""
                CALL gds.pageRank.stream({
                    nodeProjection: 'GDSTest',
                    relationshipProjection: 'LINKS'
                })
                YIELD nodeId, score
                RETURN gds.util.asNode(nodeId).name as name, score
                ORDER BY score DESC
                """)
                
                pagerank_results = list(result)
                if pagerank_results:
                    logger.info(f"   ✅ SUCCESS: PageRank worked! Results:")
                    for node in pagerank_results:
                        logger.info(f"      {node['name']}: {node['score']:.4f}")
                else:
                    logger.info("   ❌ No PageRank results")
                
                # Clean up test data
                session.run("MATCH (n:GDSTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return len(pagerank_results) > 0
                
            except Exception as e:
                logger.error(f"❌ GDS functionality test failed: {e}")
                # Clean up on error
                try:
                    session.run("MATCH (n:GDSTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def test_community_detection(self):
        """Test community detection algorithm"""
        logger.info("\n🔍 Testing Community Detection...")
        
        with self.driver.session() as session:
            try:
                # Create community test data
                session.run("""
                // Community 1
                MERGE (a1:CommunityTest {id: 1, name: 'A1'})
                MERGE (a2:CommunityTest {id: 2, name: 'A2'})
                MERGE (a3:CommunityTest {id: 3, name: 'A3'})
                
                // Community 2
                MERGE (b1:CommunityTest {id: 4, name: 'B1'})
                MERGE (b2:CommunityTest {id: 5, name: 'B2'})
                MERGE (b3:CommunityTest {id: 6, name: 'B3'})
                
                // Dense connections within communities
                MERGE (a1)-[:CONNECTS]->(a2)
                MERGE (a2)-[:CONNECTS]->(a3)
                MERGE (a3)-[:CONNECTS]->(a1)
                MERGE (b1)-[:CONNECTS]->(b2)
                MERGE (b2)-[:CONNECTS]->(b3)
                MERGE (b3)-[:CONNECTS]->(b1)
                
                // Sparse connection between communities
                MERGE (a1)-[:CONNECTS]->(b1)
                """)
                logger.info("   📊 Created community test data")
                
                # Test Louvain community detection
                result = session.run("""
                CALL gds.louvain.stream({
                    nodeProjection: 'CommunityTest',
                    relationshipProjection: 'CONNECTS'
                })
                YIELD nodeId, communityId
                RETURN gds.util.asNode(nodeId).name as name, communityId
                ORDER BY communityId, name
                """)
                
                community_results = list(result)
                
                if community_results:
                    logger.info(f"   ✅ SUCCESS: Louvain community detection worked!")
                    
                    # Group by community
                    communities = {}
                    for node in community_results:
                        comm_id = node['communityId']
                        if comm_id not in communities:
                            communities[comm_id] = []
                        communities[comm_id].append(node['name'])
                    
                    for comm_id, members in communities.items():
                        logger.info(f"      Community {comm_id}: {', '.join(members)}")
                else:
                    logger.info("   ❌ No community detection results")
                
                # Clean up
                session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return len(community_results) > 0
                
            except Exception as e:
                logger.error(f"   ❌ Community detection failed: {e}")
                # Clean up on error
                try:
                    session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def save_credentials_to_env(self, client_id, client_secret):
        """Save credentials to .env file for future use"""
        logger.info("\n💾 Saving credentials to .env file...")
        
        try:
            # Read existing .env file
            env_path = ".env"
            env_lines = []
            
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    env_lines = f.readlines()
            
            # Remove existing GDS credential lines
            env_lines = [line for line in env_lines if not line.startswith('GDS_CLIENT_ID=') and not line.startswith('GDS_CLIENT_SECRET=')]
            
            # Add new credentials
            env_lines.append(f"GDS_CLIENT_ID={client_id}\n")
            env_lines.append(f"GDS_CLIENT_SECRET={client_secret}\n")
            
            # Write back to file
            with open(env_path, 'w') as f:
                f.writelines(env_lines)
            
            logger.info("✅ Credentials saved to .env file")
            logger.info("💡 You can now use these credentials in your applications")
            
        except Exception as e:
            logger.error(f"❌ Failed to save credentials: {e}")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to set up GDS credentials"""
    logger.info("🚀 AuraDB Professional GDS Credential Setup")
    logger.info("=" * 50)
    
    try:
        setup = AuraGDSCredentialSetup()
        
        # Interactive credential setup
        success = setup.setup_credentials_interactive()
        
        if success:
            logger.info("\n🎉 Credentials configured successfully!")
            
            # Test basic functionality
            basic_test = setup.test_gds_functionality()
            
            if basic_test:
                # Test community detection
                community_test = setup.test_community_detection()
                
                if community_test:
                    logger.info("\n🎉 GDS is FULLY FUNCTIONAL!")
                    logger.info("✅ PageRank algorithm works")
                    logger.info("✅ Community detection works")
                    logger.info("✅ Your existing GDS integration code should now work!")
                    
                    # Ask if user wants to save credentials
                    save_choice = input("\nSave credentials to .env file for future use? (y/n): ").lower().strip()
                    if save_choice == 'y':
                        client_id = input("Re-enter Client ID: ").strip()
                        client_secret = getpass.getpass("Re-enter Client Secret: ").strip()
                        setup.save_credentials_to_env(client_id, client_secret)
                else:
                    logger.info("\n⚠️  Basic GDS works but community detection has issues")
            else:
                logger.info("\n❌ GDS functionality test failed")
                logger.info("💡 Please check your credentials and try again")
        else:
            logger.error("\n❌ Failed to configure GDS credentials")
        
        setup.close()
        return success and basic_test
        
    except Exception as e:
        logger.error(f"❌ GDS credential setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
