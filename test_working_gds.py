#!/usr/bin/env python3
"""
Test Working GDS Functionality

This script tests GDS functionality using the procedures that are actually available
in your AuraDB Professional instance.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingGDSTest:
    """Test GDS functionality with available procedures"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Testing Working GDS at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def test_graph_list(self):
        """Test the gds.graph.list procedure"""
        logger.info("\n🔍 Testing gds.graph.list...")
        
        with self.driver.session() as session:
            try:
                result = session.run("CALL gds.graph.list()")
                graphs = list(result)
                logger.info(f"✅ SUCCESS: Found {len(graphs)} graph projections")
                
                if graphs:
                    for graph in graphs:
                        logger.info(f"   📊 Graph: {graph['graphName']}")
                        logger.info(f"      Nodes: {graph['nodeCount']}")
                        logger.info(f"      Relationships: {graph['relationshipCount']}")
                else:
                    logger.info("   ℹ️  No existing graph projections")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ FAILED: {e}")
                return False
    
    def test_pagerank_stream(self):
        """Test PageRank algorithm using stream mode"""
        logger.info("\n🧪 Testing PageRank Stream Algorithm...")
        
        with self.driver.session() as session:
            try:
                # Create test data
                session.run("""
                MERGE (a:PageRankTest {id: 1, name: 'Node1'})
                MERGE (b:PageRankTest {id: 2, name: 'Node2'})
                MERGE (c:PageRankTest {id: 3, name: 'Node3'})
                MERGE (d:PageRankTest {id: 4, name: 'Node4'})
                MERGE (a)-[:LINKS]->(b)
                MERGE (b)-[:LINKS]->(c)
                MERGE (c)-[:LINKS]->(d)
                MERGE (d)-[:LINKS]->(a)
                MERGE (a)-[:LINKS]->(c)
                """)
                logger.info("   📊 Created test data")
                
                # Test PageRank stream
                result = session.run("""
                CALL gds.pageRank.stream({
                    nodeProjection: 'PageRankTest',
                    relationshipProjection: 'LINKS'
                })
                YIELD nodeId, score
                RETURN gds.util.asNode(nodeId).name as name, score
                ORDER BY score DESC
                """)
                
                pagerank_results = list(result)
                
                if pagerank_results:
                    logger.info(f"   ✅ SUCCESS: PageRank completed for {len(pagerank_results)} nodes")
                    for node in pagerank_results:
                        logger.info(f"      {node['name']}: {node['score']:.4f}")
                    success = True
                else:
                    logger.info("   ❌ No PageRank results")
                    success = False
                
                # Clean up
                session.run("MATCH (n:PageRankTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return success
                
            except Exception as e:
                logger.error(f"   ❌ PageRank FAILED: {e}")
                # Clean up on error
                try:
                    session.run("MATCH (n:PageRankTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def test_louvain_community_detection(self):
        """Test Louvain community detection"""
        logger.info("\n🔍 Testing Louvain Community Detection...")
        
        with self.driver.session() as session:
            try:
                # Create community test data
                session.run("""
                // Community 1
                MERGE (a1:CommunityTest {id: 1, name: 'A1'})
                MERGE (a2:CommunityTest {id: 2, name: 'A2'})
                MERGE (a3:CommunityTest {id: 3, name: 'A3'})
                
                // Community 2
                MERGE (b1:CommunityTest {id: 4, name: 'B1'})
                MERGE (b2:CommunityTest {id: 5, name: 'B2'})
                MERGE (b3:CommunityTest {id: 6, name: 'B3'})
                
                // Dense connections within communities
                MERGE (a1)-[:CONNECTS]->(a2)
                MERGE (a2)-[:CONNECTS]->(a3)
                MERGE (a3)-[:CONNECTS]->(a1)
                MERGE (b1)-[:CONNECTS]->(b2)
                MERGE (b2)-[:CONNECTS]->(b3)
                MERGE (b3)-[:CONNECTS]->(b1)
                
                // Sparse connection between communities
                MERGE (a1)-[:CONNECTS]->(b1)
                """)
                logger.info("   📊 Created community test data")
                
                # Test Louvain algorithm
                result = session.run("""
                CALL gds.louvain.stream({
                    nodeProjection: 'CommunityTest',
                    relationshipProjection: 'CONNECTS'
                })
                YIELD nodeId, communityId
                RETURN gds.util.asNode(nodeId).name as name, communityId
                ORDER BY communityId, name
                """)
                
                community_results = list(result)
                
                if community_results:
                    logger.info(f"   ✅ SUCCESS: Louvain completed for {len(community_results)} nodes")
                    
                    # Group by community
                    communities = {}
                    for node in community_results:
                        comm_id = node['communityId']
                        if comm_id not in communities:
                            communities[comm_id] = []
                        communities[comm_id].append(node['name'])
                    
                    for comm_id, members in communities.items():
                        logger.info(f"      Community {comm_id}: {', '.join(members)}")
                    
                    success = True
                else:
                    logger.info("   ❌ No Louvain results")
                    success = False
                
                # Clean up
                session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return success
                
            except Exception as e:
                logger.error(f"   ❌ Louvain FAILED: {e}")
                # Clean up on error
                try:
                    session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def test_graph_projection_workflow(self):
        """Test the full graph projection workflow"""
        logger.info("\n🏗️  Testing Graph Projection Workflow...")
        
        with self.driver.session() as session:
            try:
                # Create test data
                session.run("""
                MERGE (a:ProjectionTest {id: 1, name: 'Alpha'})
                MERGE (b:ProjectionTest {id: 2, name: 'Beta'})
                MERGE (c:ProjectionTest {id: 3, name: 'Gamma'})
                MERGE (d:ProjectionTest {id: 4, name: 'Delta'})
                MERGE (a)-[:RELATES {weight: 1.0}]->(b)
                MERGE (b)-[:RELATES {weight: 2.0}]->(c)
                MERGE (c)-[:RELATES {weight: 1.5}]->(d)
                MERGE (d)-[:RELATES {weight: 0.5}]->(a)
                """)
                logger.info("   📊 Created projection test data")
                
                # Create graph projection
                result = session.run("""
                CALL gds.graph.project(
                    'test_projection',
                    'ProjectionTest',
                    {
                        RELATES: {
                            properties: 'weight'
                        }
                    }
                )
                """)
                
                projection_info = result.single()
                logger.info(f"   ✅ Graph projection created:")
                logger.info(f"      Name: {projection_info['graphName']}")
                logger.info(f"      Nodes: {projection_info['nodeCount']}")
                logger.info(f"      Relationships: {projection_info['relationshipCount']}")
                
                # Run PageRank on the projection
                result = session.run("""
                CALL gds.pageRank.stream('test_projection')
                YIELD nodeId, score
                RETURN gds.util.asNode(nodeId).name as name, score
                ORDER BY score DESC
                """)
                
                pagerank_results = list(result)
                if pagerank_results:
                    logger.info(f"   ✅ PageRank on projection successful:")
                    for node in pagerank_results:
                        logger.info(f"      {node['name']}: {node['score']:.4f}")
                
                # Clean up projection
                session.run("CALL gds.graph.drop('test_projection')")
                logger.info("   🧹 Dropped graph projection")
                
                # Clean up data
                session.run("MATCH (n:ProjectionTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return True
                
            except Exception as e:
                logger.error(f"   ❌ Graph projection workflow FAILED: {e}")
                # Clean up on error
                try:
                    session.run("CALL gds.graph.drop('test_projection')")
                    session.run("MATCH (n:ProjectionTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def test_on_legal_data(self):
        """Test GDS on existing legal case data"""
        logger.info("\n⚖️  Testing GDS on Legal Case Data...")
        
        with self.driver.session() as session:
            try:
                # Check for existing case data
                result = session.run("MATCH (c:Case) RETURN count(c) as count")
                case_count = result.single()['count']
                
                if case_count == 0:
                    logger.info("   ℹ️  No Case nodes found - skipping legal data test")
                    return False
                
                logger.info(f"   📊 Found {case_count} Case nodes")
                
                # Test PageRank on legal cases
                result = session.run("""
                CALL gds.pageRank.stream({
                    nodeProjection: 'Case',
                    relationshipProjection: ['CITES', 'RELATES_TO']
                })
                YIELD nodeId, score
                RETURN gds.util.asNode(nodeId).title as title, score
                ORDER BY score DESC
                LIMIT 5
                """)
                
                top_cases = list(result)
                
                if top_cases:
                    logger.info(f"   ✅ SUCCESS: Top {len(top_cases)} cases by PageRank:")
                    for case in top_cases:
                        title = case['title'][:60] + "..." if len(case['title']) > 60 else case['title']
                        logger.info(f"      {title}: {case['score']:.4f}")
                    return True
                else:
                    logger.info("   ℹ️  No PageRank results (may need relationships)")
                    return False
                
            except Exception as e:
                logger.error(f"   ❌ Legal data test FAILED: {e}")
                return False
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to test working GDS functionality"""
    logger.info("🚀 Testing Working GDS Functionality in AuraDB Professional...")
    logger.info("=" * 80)
    
    try:
        tester = WorkingGDSTest()
        
        # Test basic graph operations
        graph_list_works = tester.test_graph_list()
        
        # Test algorithms
        pagerank_works = tester.test_pagerank_stream()
        louvain_works = tester.test_louvain_community_detection()
        
        # Test graph projection workflow
        projection_works = tester.test_graph_projection_workflow()
        
        # Test on legal data
        legal_data_works = tester.test_on_legal_data()
        
        # Summary
        logger.info("\n" + "=" * 80)
        logger.info("📋 WORKING GDS FUNCTIONALITY TEST RESULTS:")
        
        logger.info(f"   Graph List: {'✅' if graph_list_works else '❌'}")
        logger.info(f"   PageRank Stream: {'✅' if pagerank_works else '❌'}")
        logger.info(f"   Louvain Community Detection: {'✅' if louvain_works else '❌'}")
        logger.info(f"   Graph Projection Workflow: {'✅' if projection_works else '❌'}")
        logger.info(f"   Legal Data Analysis: {'✅' if legal_data_works else '❌'}")
        
        working_features = sum([graph_list_works, pagerank_works, louvain_works, projection_works])
        
        if working_features >= 3:
            logger.info("\n🎉 GDS is FULLY FUNCTIONAL in your AuraDB Professional!")
            logger.info("✅ You can use GDS algorithms for:")
            logger.info("   📊 Community detection (Louvain)")
            logger.info("   🔗 Centrality analysis (PageRank)")
            logger.info("   🏗️  Graph projections for complex analysis")
            logger.info("   ⚖️  Legal case relationship analysis")
            logger.info("")
            logger.info("💡 Your existing GDS integration code will work with minor adjustments")
            
        elif working_features >= 1:
            logger.info("\n⚠️  GDS is PARTIALLY FUNCTIONAL")
            logger.info("💡 Some algorithms work - you can use basic graph analytics")
            
        else:
            logger.info("\n❌ GDS is NOT FUNCTIONAL")
            logger.info("💡 Consider contacting Neo4j support about GDS availability")
        
        tester.close()
        return working_features >= 3
        
    except Exception as e:
        logger.error(f"❌ Working GDS test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
