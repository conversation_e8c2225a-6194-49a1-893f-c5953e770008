#!/usr/bin/env python3
"""
Neo4j GDS Functionality Test

This script tests actual GDS functionality to determine if the procedures
are truly available and working, not just listed.
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GDSFunctionalityTester:
    """Test actual GDS functionality"""
    
    def __init__(self):
        """Initialize connection to Neo4j"""
        self.uri = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
        self.username = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not self.password:
            raise ValueError("NEO4J_PASSWORD environment variable is required")
        
        logger.info(f"Connecting to Neo4j at: {self.uri}")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.username, self.password))
            # Test connection
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                result.single()
            logger.info("✅ Successfully connected to Neo4j")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Neo4j: {e}")
            raise
    
    def test_core_gds_functions(self):
        """Test core GDS functions one by one"""
        logger.info("\n🧪 Testing Core GDS Functions...")
        
        test_functions = [
            ("gds.version", "Get GDS version"),
            ("gds.list", "List available algorithms"),
            ("gds.graph.list", "List graph projections"),
            ("gds.graph.exists", "Check if graph exists"),
        ]
        
        results = {}
        
        with self.driver.session() as session:
            for func_name, description in test_functions:
                logger.info(f"🔍 Testing {func_name} - {description}")
                
                try:
                    if func_name == "gds.version":
                        result = session.run("CALL gds.version()")
                        data = result.single()
                        logger.info(f"   ✅ SUCCESS: GDS Version {data['gdsVersion']}")
                        results[func_name] = True
                        
                    elif func_name == "gds.list":
                        result = session.run("CALL gds.list()")
                        algorithms = list(result)
                        logger.info(f"   ✅ SUCCESS: Found {len(algorithms)} algorithms")
                        results[func_name] = True
                        
                    elif func_name == "gds.graph.list":
                        result = session.run("CALL gds.graph.list()")
                        graphs = list(result)
                        logger.info(f"   ✅ SUCCESS: Found {len(graphs)} graph projections")
                        results[func_name] = True
                        
                    elif func_name == "gds.graph.exists":
                        result = session.run("RETURN gds.graph.exists('test_graph') as exists")
                        exists = result.single()['exists']
                        logger.info(f"   ✅ SUCCESS: Function callable (test_graph exists: {exists})")
                        results[func_name] = True
                        
                except Exception as e:
                    logger.error(f"   ❌ FAILED: {e}")
                    results[func_name] = False
        
        return results
    
    def test_graph_projection(self):
        """Test creating a simple graph projection"""
        logger.info("\n🏗️  Testing Graph Projection Creation...")
        
        with self.driver.session() as session:
            try:
                # First, create some test data if it doesn't exist
                session.run("""
                MERGE (a:TestNode {id: 1, name: 'Node1'})
                MERGE (b:TestNode {id: 2, name: 'Node2'})
                MERGE (c:TestNode {id: 3, name: 'Node3'})
                MERGE (a)-[:TEST_REL]->(b)
                MERGE (b)-[:TEST_REL]->(c)
                MERGE (c)-[:TEST_REL]->(a)
                """)
                logger.info("   📊 Created test data")
                
                # Try to create a graph projection
                result = session.run("""
                CALL gds.graph.project(
                    'test_projection',
                    'TestNode',
                    'TEST_REL'
                )
                """)
                
                projection_info = result.single()
                logger.info(f"   ✅ SUCCESS: Created graph projection")
                logger.info(f"      Nodes: {projection_info['nodeCount']}")
                logger.info(f"      Relationships: {projection_info['relationshipCount']}")
                
                # Clean up - drop the projection
                session.run("CALL gds.graph.drop('test_projection')")
                logger.info("   🧹 Cleaned up test projection")
                
                return True
                
            except Exception as e:
                logger.error(f"   ❌ FAILED: {e}")
                return False
    
    def test_community_detection(self):
        """Test community detection algorithm"""
        logger.info("\n🔍 Testing Community Detection...")
        
        with self.driver.session() as session:
            try:
                # Create a larger test graph for community detection
                session.run("""
                // Create two communities
                MERGE (a1:CommunityTest {id: 1, name: 'A1'})
                MERGE (a2:CommunityTest {id: 2, name: 'A2'})
                MERGE (a3:CommunityTest {id: 3, name: 'A3'})
                MERGE (b1:CommunityTest {id: 4, name: 'B1'})
                MERGE (b2:CommunityTest {id: 5, name: 'B2'})
                MERGE (b3:CommunityTest {id: 6, name: 'B3'})
                
                // Dense connections within communities
                MERGE (a1)-[:CONNECTS]->(a2)
                MERGE (a2)-[:CONNECTS]->(a3)
                MERGE (a3)-[:CONNECTS]->(a1)
                MERGE (b1)-[:CONNECTS]->(b2)
                MERGE (b2)-[:CONNECTS]->(b3)
                MERGE (b3)-[:CONNECTS]->(b1)
                
                // Sparse connection between communities
                MERGE (a1)-[:CONNECTS]->(b1)
                """)
                logger.info("   📊 Created community test data")
                
                # Create graph projection
                session.run("""
                CALL gds.graph.project(
                    'community_test',
                    'CommunityTest',
                    'CONNECTS'
                )
                """)
                logger.info("   🏗️  Created graph projection")
                
                # Run Louvain community detection
                result = session.run("""
                CALL gds.louvain.stream('community_test')
                YIELD nodeId, communityId
                RETURN gds.util.asNode(nodeId).name as name, communityId
                ORDER BY communityId, name
                """)
                
                communities = list(result)
                logger.info(f"   ✅ SUCCESS: Community detection completed")
                logger.info(f"      Found communities for {len(communities)} nodes:")
                
                for node in communities:
                    logger.info(f"         {node['name']} → Community {node['communityId']}")
                
                # Clean up
                session.run("CALL gds.graph.drop('community_test')")
                session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                logger.info("   🧹 Cleaned up test data")
                
                return True
                
            except Exception as e:
                logger.error(f"   ❌ FAILED: {e}")
                # Try to clean up even if test failed
                try:
                    session.run("CALL gds.graph.drop('community_test')")
                    session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                except:
                    pass
                return False
    
    def cleanup_test_data(self):
        """Clean up any remaining test data"""
        logger.info("\n🧹 Cleaning up test data...")
        
        with self.driver.session() as session:
            try:
                # Remove test nodes
                session.run("MATCH (n:TestNode) DETACH DELETE n")
                session.run("MATCH (n:CommunityTest) DETACH DELETE n")
                logger.info("   ✅ Test data cleaned up")
            except Exception as e:
                logger.error(f"   ⚠️  Cleanup warning: {e}")
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            logger.info("🔌 Database connection closed")

def main():
    """Main function to test GDS functionality"""
    logger.info("🚀 Testing Neo4j GDS Functionality...")
    logger.info("=" * 60)
    
    try:
        tester = GDSFunctionalityTester()
        
        # Test core functions
        core_results = tester.test_core_gds_functions()
        
        # If core functions work, test more advanced features
        if core_results.get("gds.version", False):
            projection_success = tester.test_graph_projection()
            community_success = tester.test_community_detection()
        else:
            projection_success = False
            community_success = False
        
        # Clean up
        tester.cleanup_test_data()
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📋 GDS FUNCTIONALITY TEST RESULTS:")
        
        working_functions = sum(1 for result in core_results.values() if result)
        total_functions = len(core_results)
        
        logger.info(f"   Core Functions: {working_functions}/{total_functions} working")
        
        for func, success in core_results.items():
            status = "✅" if success else "❌"
            logger.info(f"      {status} {func}")
        
        if working_functions > 0:
            logger.info(f"   Graph Projection: {'✅' if projection_success else '❌'}")
            logger.info(f"   Community Detection: {'✅' if community_success else '❌'}")
            
            if projection_success and community_success:
                logger.info("\n🎉 GDS Library is FULLY FUNCTIONAL!")
                logger.info("💡 You can use all GDS features including community detection")
            elif working_functions == total_functions:
                logger.info("\n⚠️  GDS Library is PARTIALLY FUNCTIONAL")
                logger.info("💡 Core functions work but advanced features may have issues")
            else:
                logger.info("\n❌ GDS Library has LIMITED FUNCTIONALITY")
                logger.info("💡 Some core functions work but reliability is questionable")
        else:
            logger.info("\n❌ GDS Library is NOT FUNCTIONAL")
            logger.info("💡 Procedures are listed but cannot be executed")
        
        tester.close()
        return working_functions > 0 and projection_success
        
    except Exception as e:
        logger.error(f"❌ GDS functionality test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
